import argparse
import asyncio
import logging
import os
import sys
from typing import Optional

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
if src_path not in sys.path:
    sys.path.append(src_path)

from models import webhook_server
from utils import server_utils, self_log


if __name__ == "__main__":
    # 初始化临时logger用于命令行启动日志
    temp_logger: Optional[logging.Logger] = logging.getLogger(__name__) if self_log.log_config.had_init() else None
    server_utils.logger_print(msg="webhook server command line startup", custom_logger=temp_logger)
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    args = parser.parse_args()
    server_utils.logger_print(msg=f"parsed command line arguments: config={args.config}", custom_logger=temp_logger)
    server_utils.logger_print(msg="starting async server execution", custom_logger=temp_logger)
    asyncio.run(webhook_server.run_server_with_path(args.config))
