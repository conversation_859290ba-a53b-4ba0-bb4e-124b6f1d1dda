# 使用ttkbootstrap库实现GUI界面的工具类
import logging
import tkinter
from tkinter import font
from typing import Union, Optional

import ttkbootstrap as ttk
from ttkbootstrap.dialogs import MessageDialog

from config import gui_constants
from models.gui_widgets import FloatMeter, NotificationBar
from utils import server_utils, gui_utils

logger = logging.getLogger(__name__)




first_available_font_family:Optional[str]=None
def set_available_unified_font_families(main_win: ttk.Window):
    """
    给当前gui主界面设置统一的可用的字体
    font name统一，每一个控件可以单独修改字体，但是整体上是使用同一种字体
    注意事项：必须在其他控件之前设置字体列表，否则已有的控件不会生效


    :param main_win: ttkbootstrap.Window 当前gui主界面
    """
    global first_available_font_family
    if not main_win:
        server_utils.logger_print(msg="set available font families failed,main_win is None",custom_logger=logger,log_level=logging.ERROR)
        raise ValueError("set available font families failed,main_win is None")


    def get_first_available_font_family():
        """加载第一个有效的字体"""
        all_font_families=set(font.families())
        for family in gui_constants.FONT_FAMILIES:
            if family in all_font_families:
                return family
        return font.nametofont("TkDefaultFont").actual()["family"]
    first_available_font_family=get_first_available_font_family()

    cur_font=font.Font(family=first_available_font_family,size=11)
    main_win.option_add('*Font',cur_font)
    ttk.Style().configure(".", font=cur_font)

def dialog_center_in_parent(parent:Union[tkinter.Tk,tkinter.Toplevel], dialog:MessageDialog):
    """对话框在父窗口居中显示"""
    parent.update_idletasks()

    pw = parent.winfo_width()
    ph = parent.winfo_height()
    px = parent.winfo_rootx()
    py = parent.winfo_rooty()

    dw = gui_constants.MESSAGE_DIALOG_WIDTH
    dh = gui_constants.MESSAGE_DIALOG_HEIGHT

    x = px + (pw - dw) // 2
    y = py + (ph - dh) // 2
    dialog.show(position=(x, y))

def comm_main_win_do(main_win: ttk.Window):
    """通用主界面通用操作"""
    if not main_win:
        server_utils.logger_print(msg="comm main win do failed,main_win is None",custom_logger=logger,log_level=logging.ERROR)
        raise ValueError("comm main win do failed,main_win is None")
    set_available_unified_font_families(main_win)
    gui_utils.center_main_window(main_win)

def comm_child_win_do(child_win:tkinter.Toplevel,parent_win:Union[tkinter.Tk, tkinter.Toplevel]):
    """设置子窗口的通用操作"""
    child_win.withdraw()  # 先隐藏窗口
    # child_win.attributes("-toolwindow", True)  # 避免出现闪烁
    gui_utils.center_child_window(child_win,parent_win)
    child_win.grab_set()

    child_win.resizable(False, False)
    child_win.bind("<Escape>", lambda e: child_win.destroy())
    child_win.focus_force()
    child_win.deiconify()


def create_cpu_meter(parent,metersize,meterthickness, x=0, y=0):
    meter = FloatMeter(
        master=parent,
        metersize=metersize, # 仪表盘直径
        meterthickness=meterthickness, # 仪表盘圆弧的粗细程度
        stripethickness=0,
        wedgesize=0,
        amountused=0.0,  # 使用浮点数
        amounttotal=100.0,  # 使用浮点数
        amountformat="{:.1f}",  # 浮点数格式
        metertype="full",
        subtext="CPU 利用率",
        interactive=False,
        textleft="",
        textright="%",
        textfont=("Helvetica", 18, "bold"),
        subtextfont=("Helvetica", 10),
        bootstyle="info"
    )
    meter.place(x=x, y=y)

    return meter

default_segment_style={50:"success",80:"warning",100:"danger"}
def update_cpu_meter(meter, value,segment_style:dict[int,str]=None):
    """
    更新仪表盘显示
    根据仪表盘区域的阈值和样式，自动调整样式
    :param meter: ttkbootstrap.widgets.meter.FloatMeter 仪表盘控件
    :param value: float 仪表盘显示的值
    :param segment_style: dict[int,str] 阈值和样式的映射关系 其key必须按照从小到大排列
    """
    if segment_style is None:
        segment_style=default_segment_style
    cur_style="info"
    for segment_threshold in segment_style.keys():
        if value < segment_threshold:
            cur_style = segment_style[segment_threshold]
            break

    # 应用样式
    meter.configure(bootstyle=cur_style,amountused=value)

    print(f"CPU 利用率: {value:.1f}%")



def show_notification(win:ttk.Window, text:str, font_name_size:tuple[str, int]):
    """设置在主界面上显示通知栏"""
    notification_container = ttk.Frame(win)
    notification_container.pack(side="top", fill="x")
    # 禁止自动缩放，这样即便子控件被隐藏，容器高度也不会改变
    notification_container.pack_propagate(False)
    # 固定容器高度，与 Marquee 一致
    notification_container.configure(height=font_name_size[1] + 8 + 8)  # font size(10) + padding(8)

    # 在容器中放入通知栏
    notif = NotificationBar(notification_container, text=text, font_name_size=font_name_size)
    notif.pack(fill="both", expand=True)
