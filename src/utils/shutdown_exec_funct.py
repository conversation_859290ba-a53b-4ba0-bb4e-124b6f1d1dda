# 在这个时候,logging模块可能已经关闭,这种情况下就不能使用logging了
import asyncio
import atexit
import inspect
import logging
import os
import signal
import sys
import threading
from typing import List, Callable, Any, Tuple, Dict

from utils import server_utils

_registered_functions: List[Tuple[int, Callable[..., Any], Tuple[Any, ...], Dict[str, Any],bool]] = []
_next_id = 0  # 用于生成唯一标识符
_executed = False
_registration_lock = threading.Lock()
_original_excepthook = sys.excepthook
# 保存原信号处理函数
_original_signal_handlers: Dict[int, Any] = {}

logger = logging.getLogger(__name__)
def _run_coroutine_in_thread(coro: asyncio.coroutines,wait:bool) -> None:
    """在单独线程中运行协程,以绕过已有事件循环限制"""
    server_utils.logger_print(f"starting coroutine in thread, wait={wait}", logger, log_level=logging.INFO)
    def _runner():
        server_utils.logger_print("coroutine thread runner started", logger, log_level=logging.INFO)
        try:
            asyncio.run(coro)
            server_utils.logger_print("coroutine execution completed successfully", logger, log_level=logging.INFO)
        except Exception as coro_except:
            server_utils.logger_print("error running coroutine in thread!", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=coro_except)

    thread = threading.Thread(target=_runner,daemon=not wait)
    server_utils.logger_print(f"created thread for coroutine, daemon={not wait}", logger, log_level=logging.INFO)
    thread.start()
    server_utils.logger_print("coroutine thread started", logger, log_level=logging.INFO)

    if wait:
        server_utils.logger_print("waiting for coroutine thread to complete", logger, log_level=logging.INFO)
        try:
          thread.join()
          server_utils.logger_print("coroutine thread completed successfully", logger, log_level=logging.INFO)
        except Exception as e:
            server_utils.logger_print("error waiting for coroutine thread to finish!", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)
    else:
        server_utils.logger_print("coroutine thread started in background (no wait)", logger, log_level=logging.INFO)


def _run_function(func: Callable[..., Any],wait:bool, *args, **kwargs):
    function_name = getattr(func, '__name__', str(func))
    server_utils.logger_print(f"executing function {function_name} before shutting down, wait={wait}, args={args}, kwargs={kwargs}", logger,log_level=logging.INFO)
    try:
        # 协程函数返回的是协程对象:所以实现就不需要区分是否是协程函数
        server_utils.logger_print(f"calling function {function_name}", logger, log_level=logging.INFO)
        res = func(*args, **kwargs)
        server_utils.logger_print(f"function {function_name} call completed, result type: {type(res)}", logger, log_level=logging.INFO)
    except Exception as e:
        server_utils.logger_print(f"executing function {function_name} before shutting down... occurred an exception!", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)
        return
    if inspect.iscoroutine(res):
        # 是协程函数 async def
        server_utils.logger_print(f"function {function_name} returned coroutine, running in thread", logger, log_level=logging.INFO)
        _run_coroutine_in_thread(res,wait)
    else:
        server_utils.logger_print(f"function {function_name} execution completed (non-coroutine)", logger, log_level=logging.INFO)

def _execute_registered_functions() -> None:
    global _executed
    with _registration_lock:
        if _executed:
            return
        _executed = True
        funcs_to_call = [(func, args, kwargs, wait) for _, func, args, kwargs, wait in reversed(_registered_functions)]
    for func, args, kwargs,wait in funcs_to_call:
        try:
            _run_function(func,wait, *args, **kwargs)
        except BaseException as e:
            server_utils.logger_print(f"executing function {getattr(func, '__name__', str(func))} before shutting down... occurred an exception!", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)


# 在接收到对应的信号之后,执行当前已经注册的函数,之后再执行原信号处理函数
def _signal_handler(signum, frame) -> None:
    server_utils.logger_print(f"received signal {signum},executing registered functions before shutting down...", logger,log_level=logging.INFO)
    _execute_registered_functions()
    original_handler = _original_signal_handlers.get(signum, signal.SIG_DFL)
    # 恢复原信号处理函数
    try:
        signal.signal(signum, original_handler)
    except Exception: # noqa
        pass
    # 重新触发信号以执行原逻辑
    if original_handler == signal.SIG_DFL:
        try:
            if hasattr(signal, 'raise_signal'):
                signal.raise_signal(signum)
            else:
                os.kill(os.getpid(), signum)
        except Exception:# noqa
            pass
    elif original_handler != signal.SIG_IGN:
        try:
            original_handler(signum, frame)
        except Exception:# noqa
            pass


def _custom_excepthook(exc_type, exc_value, exc_traceback) -> None:
    server_utils.logger_print(f"uncaught exception occurred, executing registered functions before shutting down...\n {exc_value}", logger,log_level=logging.INFO)
    _execute_registered_functions()
    _original_excepthook(exc_type, exc_value, exc_traceback)


# 自定义线程异常处理函数,该代码实现的关闭钩子和线程异常处理无关

atexit.register(_execute_registered_functions)
sys.excepthook = _custom_excepthook
# 兼容旧版本线程异常处理（仅记录，不执行关闭钩子） 当当前不存在子线程异常处理时才执行以下程序
if not hasattr(threading, 'excepthook'):
    # 兼容旧版本：通过包装线程函数捕获异常 [线程不需要执行关闭钩子的函数]
    _original_threading_start = threading.Thread.start
    def _patched_thread_start(self) -> None:
        original_run = self.run

        def _wrapped_run(*run_args, **run_kwargs) -> None:
            try:
                original_run(*run_args, **run_kwargs)
            except Exception as e:
                server_utils.logger_print(f"uncaught exception occurred in thread {self.name}.", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)

        self.run = _wrapped_run
        _original_threading_start(self)

    threading.Thread.start = _patched_thread_start



if os.name == 'posix':
    _signals = [signal.SIGTERM, signal.SIGINT, signal.SIGQUIT, signal.SIGHUP]
elif os.name == 'nt':
    _signals = [signal.SIGTERM, signal.SIGINT, signal.SIGBREAK]
else:
    _signals = []

# 保存原信号处理函数并注册新的信号处理函数
for sig in _signals:
    try:
        _original_signal_handlers[sig] = signal.getsignal(sig)
        signal.signal(sig, _signal_handler)
    except (ValueError, AttributeError, OSError):
        server_utils.logger_print(f"failed to register new signal handler for signal {sig}", logger,log_level=logging.ERROR,print_error=True)
        pass

def unregister(registration_id: int) -> bool:
    """通过注册ID取消注册关闭钩子函数"""
    with _registration_lock:
        if _executed:
            return False

        # 查找并移除指定ID的注册项
        for i, item in enumerate(_registered_functions):
            if item[0] == registration_id:
                del _registered_functions[i]
                return True
        return False

def unregister_function(func: Callable, *args, **kwargs) -> bool:
    """通过函数引用和参数取消注册关闭钩子函数"""
    with _registration_lock:
        if _executed:
            return False

        # 查找并移除匹配的注册项
        for i, item in enumerate(_registered_functions):
            _, registered_func, registered_args, registered_kwargs, _ = item
            if server_utils.is_same_function(func, args, kwargs,
                                 registered_func, registered_args, registered_kwargs):
                del _registered_functions[i]
                return True
        return False

# 注册关闭时执行的函数,当注册时机是执行完关闭函数之后时,则直接执行当前对应函数
def register(func: Callable[..., Any], wait: bool = False, *args, **kwargs) -> int:
    """
    注册一个关闭钩子函数，返回注册ID用于取消注册

    Args:
        func: 要执行的 callable
        wait: 如果 func 是 coroutine function 或返回协程，是否"同步等待"其完成
        args, kwargs: 调用 func 的参数
    Returns:
        int: 注册ID，可用于取消注册
    """
    function_name = getattr(func, '__name__', str(func))
    server_utils.logger_print(f"registering shutdown function: {function_name}, wait={wait}, args={args}, kwargs={kwargs}", logger, log_level=logging.INFO)

    if not callable(func):
        server_utils.logger_print(f"registration failed: {function_name} is not callable", logger, log_level=logging.ERROR)
        raise TypeError("argument must be callable")

    with _registration_lock:
        global _next_id
        server_utils.logger_print(f"checking for existing registration of function: {function_name}", logger, log_level=logging.INFO)

        # 检查是否已存在相同注册
        for item in _registered_functions:
            _, registered_func, registered_args, registered_kwargs, _ = item
            if server_utils.is_same_function(func, args, kwargs,
                                 registered_func, registered_args, registered_kwargs):
                # 存在相同注册，返回已存在的ID
                server_utils.logger_print(f"function {function_name} already registered with id: {item[0]}", logger, log_level=logging.INFO)
                return item[0]

        registration_id = _next_id
        _next_id += 1
        server_utils.logger_print(f"assigning new registration id: {registration_id} to function: {function_name}", logger, log_level=logging.INFO)

        if _executed:
            server_utils.logger_print(f"shutdown already executed, running function {function_name} immediately", logger, log_level=logging.INFO)
            try:
                _run_function(func,wait, *args,**kwargs)
            except Exception as e:
                server_utils.logger_print(f"executing function {function_name} occurred an exception when registering!", logger,log_level=logging.ERROR,use_exception=True,print_error=True,exception=e)
        else:
            server_utils.logger_print(f"adding function {function_name} to shutdown registry", logger, log_level=logging.INFO)
            _registered_functions.append((registration_id, func, args, kwargs,wait))

        server_utils.logger_print(f"function {function_name} registered successfully with id: {registration_id}", logger, log_level=logging.INFO)
        return registration_id
