# 在gui主界面动态显示的服务端数据信息
from typing import Optional, List

from models import server_data


class GUIServerInfo:
    def __init__(self):
        # 服务端运行时长由前端gui自己计算:因为后台线程和前端gui界面显示不一致导致显示的存在误差和不准确的情况
        self.exit_code:Optional[int] = None
        self.exit_reason:Optional[str] = None
        self.server_status_message:Optional[str] = None
        self.server_status_color:Optional[str] = None
        # 获取列表中需要的数据,cur_get_new_data 用于判断是否是新获取到的数据;new_data 数据使用完之后需要更新cur_get_new_data值为False
        self.cur_get_new_data:bool = False
        self.new_data:Optional[List[server_data.MessageToGUI]] = None
        # 服务端运行按钮 start_btn 是否可以使用
        self.server_button_flag:bool = False
        # 是否执行服务端停止操作 --- 可能因为一些场景在后台线程执行逻辑中会让服务端停止;比如:执行出现异常时
        self.stop_server_flag:bool = False


    def get_data(self)->str|None:
        self.cur_get_new_data = False
        return self.new_data
