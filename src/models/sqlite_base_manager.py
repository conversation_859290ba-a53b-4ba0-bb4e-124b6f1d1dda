import logging
import os
import random
import sqlite3
import threading
import time
from contextlib import contextmanager
from datetime import datetime
from typing import Any, Callable, Generator, Optional
from zoneinfo import ZoneInfo

from config import constants, gui_constants
from models import sql_log
from utils import shutdown_exec_funct, server_utils

logger = logging.getLogger(__name__)

class SQLiteBaseManager:
    """
    SQLite数据库操作基类（线程安全，WAL模式，自动重试）
    注意事项：
    1 由于写入数据库中的时间字段的时区是UTC,在使用SQL时也是UTC,但SELECT返回的是UTC时区,可能不是我所需要的,这时就需要手动转换时区,使用SQLiteBaseManager.convert_utc_str()函数

    SQL字符串添加和使用注意事项:
        sql就函数内部的，不能放到外部共享使用
        单行的sql使用""即可,多行的使用三个引号包含,需要注意是三个引号包含,不能多加引号
        对应sql字符串命名注意不能在函数内重复
    """
    _started_paths = set()
    def __init__(self, db_path: str, zone: ZoneInfo = None, enable_sql_logging: bool = True):
        """
        Args:
            db_path: 数据库文件路径[传入文件的绝对路径]
            enable_sql_logging: 是否启用SQL日志追踪(default=True)
        """
        # 由于SQLite不支持时区,目前其使用在查询数据返回的时间字段转换时区需要使用
        self.zone = zone or constants.DEFAULT_TIMEZONE
        resolved_path = server_utils.get_real_path_create_dir(db_path)
        server_utils.logger_print(msg=f"initializing sqlite base manager with db_path: {resolved_path}, sql_logging: {enable_sql_logging}",custom_logger=logger)
        self.db_path = resolved_path
        self.enable_sql_logging = enable_sql_logging
        server_utils.logger_print(msg="calling database initialization",custom_logger=logger)
        self._init_db()
        server_utils.logger_print(msg=f"sqlite manager initialized successfully with db_path={resolved_path}",custom_logger=logger)
        # 每一个数据库文件都补充一个 wal_loop 和 check_integrity_loop 线程
        if resolved_path not in SQLiteBaseManager._started_paths:
            SQLiteBaseManager._started_paths.add(resolved_path)
            self._stop_event = threading.Event()
            t_wal = threading.Thread(target=self._wal_loop, daemon=True)
            t_wal.start()
            t_integrity = threading.Thread(target=self._check_integrity_loop, daemon=True)
            t_integrity.start()
            server_utils.logger_print(msg=f"[{resolved_path}] wal and integrity check threads started",custom_logger=logger)
            shutdown_exec_funct.register(self._event_quit)

    def _init_db(self):
        """初始化数据库（由子类实现表结构）"""
        raise NotImplementedError("Subclasses must implement _init_db")

    @contextmanager
    def _get_db_cursor(self) -> Generator[sqlite3.Cursor, None, None]:
        """数据库连接上下文管理器[支持WAL模式]:保证单表写互斥"""
        logger.info("creating database connection and cursor")
        conn: Optional[sqlite3.Connection] = None
        cursor: Optional[sqlite3.Cursor] = None
        try:
            # 动态构造连接参数
            connect_args = {
                "database": self.db_path,
                "timeout": 20.0,
                "isolation_level": None
            }
            if self.enable_sql_logging:
                connect_args["factory"] = sql_log.LoggingConnection
                logger.info("sql logging enabled for connection")

            logger.info(f"connecting to database: {self.db_path}")
            conn = sqlite3.connect(**connect_args)
            logger.info("database connection established")

            logger.info("setting wal mode and synchronous normal")
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")

            cursor = conn.cursor()
            logger.info("database cursor created")

            logger.info("beginning immediate transaction")
            cursor.execute("BEGIN IMMEDIATE;")

            logger.info("yielding cursor for operation")
            yield cursor

            logger.info("committing transaction")
            conn.commit()
            logger.info("transaction committed successfully")

        except sqlite3.Error:
            logger.error("sqlite exception occurred when executing database operation!")
            if conn:
                try:
                    logger.info("rolling back transaction due to sqlite error")
                    conn.rollback()
                    logger.info("transaction rolled back successfully")
                except Exception as rollback_error:
                    logger.error(f"failed to rollback transaction: {rollback_error}")
            raise
        except Exception:
            logger.error("non-sqlite exception occurred when executing database operation!")
            if conn:
                try:
                    logger.info("rolling back transaction due to non-sqlite error")
                    conn.rollback()
                    logger.info("transaction rolled back successfully")
                except Exception as rollback_error:
                    logger.error(f"failed to rollback transaction: {rollback_error}")
            raise
        finally:
            # 确保资源清理 - 按照正确的顺序关闭
            if cursor:
                try:
                    logger.info("closing database cursor")
                    cursor.close()
                    logger.info("database cursor closed successfully")
                except Exception as cursor_error:
                    logger.error(f"error closing cursor: {cursor_error}")
            if conn:
                try:
                    logger.info("closing database connection")
                    conn.close()
                    logger.info("database connection closed successfully")
                except Exception as conn_error:
                    logger.error(f"error closing connection: {conn_error}")
            logger.info("database resources cleaned up")

    def _exec_retry(self, exec_funct: Callable[[sqlite3.Cursor], Any]) -> Any:
        """
        带重试机制的事务执行:
        使用注意事项:
             exec_funct函数不能嵌套执行_exec_retry的函数调用,会出现死锁的情况
             exec_funct函数不能是 functools.partial包装的函数
        """
        max_retry = 3
        base_delay = 0.1
        last_error: Optional[Exception] = None
        logger.info(f"starting database operation with retry mechanism, max_retry={max_retry}")

        for attempt in range(max_retry):
            logger.info(f"database operation attempt {attempt + 1}/{max_retry}")
            try:
                with self._get_db_cursor() as cursor:
                    logger.info("executing database function")
                    # 执行成功无异常,返回结果
                    result = exec_funct(cursor)
                    logger.info("database operation completed successfully")
                    return result
            except sqlite3.OperationalError as e:
                error_msg = str(e).lower()
                logger.warning(f"sqlite operational error on attempt {attempt + 1}: {error_msg}")

                if "locked" not in error_msg and "busy" not in error_msg:
                    logger.error(f"non-retryable sqlite error: {error_msg}")
                    raise

                if attempt < max_retry - 1:  # 不是最后一次尝试
                    # 随机抖动
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    time.sleep(delay)

                last_error = e
            except Exception:
                logger.error("non-sqlite exception occurred when executing database operation!")
                raise

        logger.error(f"all {max_retry} database operation attempts failed")
        logger.error("sqlite exception occurred when executing database operation!")
        raise last_error

    # 检查WAL文件大小并在大于[MAX_WAL_SIZE_MB]MB时执行检查点[可以作为定时任务,也可以单独自主调用]
    def check_wal_size(self):
        wal_file = f"{self.db_path}-wal"
        if os.path.exists(wal_file) and os.path.getsize(wal_file) > constants.MAX_WAL_SIZE_MB * 1024 ** 2:
            with sqlite3.connect(self.db_path) as conn:
                server_utils.logger_print(msg=f"wal file size exceeds {constants.MAX_WAL_SIZE_MB}MB, executing wal_checkpoint(FULL)",custom_logger=logger)
                conn.execute("PRAGMA wal_checkpoint(FULL);")

    # 执行完整性检查[可以作为定时任务,也可以单独自主调用]
    def check_integrity(self):
        with sqlite3.connect(self.db_path) as conn:
            result = conn.execute("PRAGMA integrity_check;").fetchone()
            if result and result[0] != "ok":
                server_utils.logger_print(msg=f"database integrity check failed: {result[0]}",custom_logger=logger,log_level=logging.ERROR,print_error=True)
                raise sqlite3.DatabaseError(f"database integrity check failed: {result[0]}")

    def _event_quit(self):
        if hasattr(self, "_stop_event"):
            self._stop_event.set()
            server_utils.logger_print(msg=f"[{self.db_path}] sqlite base manager stopped",custom_logger=logger)
    def _wal_loop(self):
        """
        每小时执行一次 WAL 检查：
         - 立即执行一次，之后每隔一小时执行
        """
        # 先执行一次
        while not self._stop_event.is_set():
            try:
                self.check_wal_size()
            except Exception:# noqa
                server_utils.logger_print(msg=f"[{self.db_path}] check_wal_size run failed",custom_logger=logger,log_level=logging.ERROR,print_error=True,use_exception=True)
            # 等待一小时，或在 stop_event 时提前退出
            if self._stop_event.wait(timeout=3600):
                break
    def _check_integrity_loop(self):
        """
        每小时执行一次 check_integrity 检查：
         - 立即执行一次，之后每隔一小时执行
        """
        # 先执行一次
        while not self._stop_event.is_set():
            try:
                self.check_integrity()
            except Exception:# noqa
                server_utils.logger_print(msg=f"[{self.db_path}] check_integrity run failed",custom_logger=logger,log_level=logging.ERROR,print_error=True,use_exception=True)
            # 等待一小时，或在 stop_event 时提前退出
            if self._stop_event.wait(timeout=3600):
                break
    @staticmethod
    def get_field_datas(rows, field_index:int, need_only:bool=False)->set[str]:
        """提取指定字段的配置数据"""
        cur_count=len(rows)
        res={row[field_index] for row in rows}
        if need_only and cur_count != len(res):
            raise ValueError(f"字段{field_index}存在重复值")
        return res
    # 将数据库默认的UTC时区转换成自己的时区
    @staticmethod
    def convert_utc_str(date_str: str, cur_zone: ZoneInfo):
        # 解析不带时区的UTC时间
        utc_time = datetime.strptime(date_str, gui_constants.DATETIME_FORMAT).replace(tzinfo=constants.UTC_ZONE)
        return utc_time.astimezone(cur_zone).strftime(gui_constants.DATETIME_FORMAT)
