class SingleInstanceMeta(type):
    """
    元类，确保使用此元类的类在一个进程中只能被实例化一次。
    第二次尝试实例化时会抛出异常。
    """
    def __init__(cls, name, bases, namespace):
        super().__init__(name, bases, namespace)
        cls._instance = None  # 用于存储实例的引用

    def __call__(cls, *args, **kwargs):
        if cls._instance is not None:
            raise RuntimeError(f"{cls.__name__} 已经被实例化过，不能重复创建")

        # 创建实例
        cls._instance = super().__call__(*args, **kwargs)
        return cls._instance
