import logging
import os
import sys
import tempfile

import pytest

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)
logging.basicConfig(
    level=logging.DEBUG,  # 设置日志级别（DEBUG 及以上级别的日志都会被记录）
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 日志格式
    datefmt='%Y-%m-%d %H:%M:%S'  # 时间格式
)
logger = logging.getLogger(__name__)

from utils import server_utils



# 测试数据生成器
@pytest.fixture
def generate_test_files(tmp_path):
    files = []

    # 创建UTF-8文件
    utf8_files = [
        ("Hello World", "valid_ascii.txt"),
        ("中文畜电池", "valid_chinese.txt"),
        ("\ufeff dcsc", "valid_bom.txt"),
        ("", "empty_file.txt"),
    ]

    # 创建非UTF-8文件
    non_utf8_files = [
        ("销售计划cds", "gbk_encoded.txt", "gbk"),
        ("当时cd破façade,", "iso8859.txt", "iso-8859-1"),
        ("dsc；😊‘、。；开机 都坚持", "utf32_le.txt", "utf-32-le"),
    ]

    for content, name in utf8_files:
        path = tmp_path / name
        path.write_text(content, encoding="utf-8")
        files.append((path, True))

    for content, name, encoding in non_utf8_files:
        path = tmp_path / name
        path.write_bytes(content.encode(encoding, errors="ignore"))
        files.append((path, False))

    return files

# 测试用例
def test_utf8_encoding_file(generate_test_files):
    """测试不同编码文件的识别结果"""
    for file_path, expected in generate_test_files:
        result = server_utils.is_utf8_encoding_file(file_path)
        assert result == expected, \
        f"Expected {expected} for file {file_path} but got {result}"

def test_nonexistent_file():
    """测试文件不存在的情况"""
    non_existent = "/path/that/does/not/exist.txt"
    assert not server_utils.is_utf8_encoding_file(non_existent)

def test_unreadable_file(tmp_path):
    """测试不可读文件的情况"""
    # 创建文件后移除读取权限
    file_path = tmp_path / "unreadable.txt"
    file_path.write_text("test")
    file_path.chmod(0o000)  # 无权限[不能在Windows上生效]

    try:
        assert not server_utils.is_utf8_encoding_file(str(file_path))
    finally:
        file_path.chmod(0o644)  # 恢复权限

def test_large_utf8_file():
    """测试大文件处理能力"""
    with tempfile.NamedTemporaryFile("wb", delete=False) as tmp:
        # 生成1MB的UTF-8文本
        large_content = b"a" * 500000 + b"\xc3\xa9" * 250000  # é字符
        tmp.write(large_content)
        file_path = tmp.name

    try:
        assert server_utils.is_utf8_encoding_file(file_path, chunk_size=1024)
    finally:
        os.unlink(file_path)

def test_special_characters_in_path():
    """测试路径包含特殊字符的情况"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        special_path = os.path.join(tmp_dir, "file with spaces and !@#$%^&()[]")
        with open(special_path, "wb") as f:
            f.write(b"Valid UTF-8")

        assert server_utils.is_utf8_encoding_file(special_path)

def test_partial_utf8_file():
    """测试部分有效UTF-8文件"""
    with tempfile.NamedTemporaryFile("wb", delete=False) as tmp:
        # 前部分有效 + 后部分无效
        content = b"Valid UTF-8: \xc3\xa9" + b"\xff\xff" * 10
        tmp.write(content)
        file_path = tmp.name

    try:
        assert not server_utils.is_utf8_encoding_file(file_path)
    finally:
        os.unlink(file_path)
