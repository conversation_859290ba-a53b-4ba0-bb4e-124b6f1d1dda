# 根据程序路径平顺杀死程序,非强制关闭
# 不能实现在Windows环境下模拟发送 Ctrl+C 信号,该代码实现就有些鸡肋了
import os
import sys

import psutil
import win32con

from utils import server_utils

is_windows = (os.name == 'nt')


def kill_program_path(program_path: str):
    if not os.path.exists(program_path):
        print(f"Error: path not found: {program_path}")
        sys.exit(1)

    target_path = server_utils.get_real_exist_file_path(program_path)
    current_pid = os.getpid()
    procs = []
    for proc in psutil.process_iter(['pid', 'exe', 'cmdline']):
        if proc.info['pid'] == current_pid:
            continue
        try:
            exe_path = proc.info['exe'] or ''
            cmdline = proc.info['cmdline'] or []
            # 将进程的 exe/cmdline 同样做 realpath 处理
            exe_real = os.path.realpath(exe_path) if exe_path else ''
            # 把所有 cmdline 参数连接并对每个参数做 realpath
            cmd_real = ' '.join(
                os.path.realpath(arg) if os.path.exists(arg) else arg
                for arg in cmdline
            )

            # 匹配：要么 exe 完全相等，要么命令行包含脚本的真实路径
            if exe_real == target_path or target_path in cmd_real:
                procs.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    if not procs:
        print(f'No process found for {program_path}')
        sys.exit(0)

    for proc in procs:
        print(f'Killing PID:{proc.info["pid"]} ({proc.info["exe"]} {proc.info["cmdline"]})')
        try:
            # os.kill(proc.info['pid'], signal.SIGINT)
            proc.send_signal(win32con.CTRL_C_EVENT)
            # if is_windows:
            #     proc.send_signal(signal.CTRL_BREAK_EVENT)
            # else:
            #     proc.send_signal(signal.SIGTERM)
        except ProcessLookupError:
            continue
    print(f'Killed {len(procs)} processes for {program_path}')


def main():
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} /full/path/to/your_script.py")
        sys.exit(1)

    kill_program_path(sys.argv[1])


if __name__ == '__main__':
    # kill_program_path('../tests/demo_function.py')
    main()
