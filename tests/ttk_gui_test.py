
import tkinter as tk
from tkinter import ttk

from fontTools.ttLib import TTFont

font_path="D://Git//python-samples-hub//resources//NotoSansSC-VariableFont_wght.ttf"

def get_font_name_from_file(path):
    font = TTFont(path)
    name = ""
    for record in font['name'].names:
        if record.nameID == 1 and record.platformID == 3:  # NameID 1: Font Family
            name = record.string.decode('utf-16-be')
            break
    return name
def style_font_test():
    root = tk.Tk()
    style = ttk.Style(root)
    font_name = get_font_name_from_file(font_path)
    style.font_create(font_name, font=font_path)
    style.configure(".", font=get_font_name_from_file(font_path))
    label = ttk.Label(root, text="Hello, world!")
    label.pack()
    root.mainloop()

if __name__ == '__main__':
    style_font_test()
