# 对`server_data_manager.py`的测试代码
import logging
import os
import re
import sqlite3
import sys
from zoneinfo import ZoneInfo

from config import gui_constants
from models.sqlite_base_manager import SQLiteBaseManager

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from utils import self_log
from models import server_data_manager
import config.constants as constants

self_log.setup_logging("../resources/log.ini",filename_prefix="test")
logger = logging.getLogger(__name__)

db_path = "../data/test.db"
# 初始化数据库并启动定时任务
date_manager = server_data_manager.WebhookDataManager(db_path, constants.DEFAULT_TIMEZONE)


# 当前python中的sqlite是否支持delete中使用limit
def check_delete_limit_supported():
    # 1. 连接到 SQLite 数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 2. 查询 SQLite 版本
        cursor.execute("SELECT sqlite_version();")
        version_str = cursor.fetchone()[0]  # 返回版本字符串，如 "3.40.1"
        logger.info(f"Current SQLite version: {version_str}")

        # 3. 解析版本字符串为元组
        match = re.match(r'(\d+)\.(\d+)\.(\d+)', version_str)
        if not match:
            raise ValueError(f"Invalid version format: {version_str}")

        major, minor, patch = map(int, match.groups())
        sqlite_version_tuple = (major, minor, patch)

        # 4. 判断是否支持 DELETE ... LIMIT
        if sqlite_version_tuple >= (3, 6, 19):
            logger.info("Current SQLite version supports DELETE ... LIMIT")
        else:
            logger.info("Current SQLite version does not support DELETE ... LIMIT")

    finally:
        # 关闭连接
        conn.close()


# 直接使用对应的函数有效性测试
def check_delete_limit_supported_2():
    try:
        date_manager.remove_excess_read_data(100001)
        logger.info("Current SQLite version supports DELETE ... LIMIT")
    except sqlite3.OperationalError:
        logger.exception(f"Current SQLite version does not support DELETE ... LIMIT.\n error!")


def utc_time_change_test():
    utc_time_str = '2025-05-18 14:40:22'
    changed_time = SQLiteBaseManager.convert_utc_str(utc_time_str,ZoneInfo(gui_constants.TIME_ZONE))
    logger.info(f"UTC time: {utc_time_str}, changed time: {changed_time}")


# 接收一次未读消息然后读取消息测试
def receive_read_msg_test():
    message_id = date_manager.save_message("Test message", "client_unique_key")
    logger.info(f"message saved, message_id: {message_id}")
    print(date_manager.get_oldest_unread(10))


if __name__ == '__main__':
    # utc_time_change_test()
    receive_read_msg_test()
    # logger.info(constants.CLIENT_KEY_PATTERN)
    # check_delete_limit_supported()
    check_delete_limit_supported_2()

    # 保持主线程运行
    # while True:
    #     time.sleep(1)
