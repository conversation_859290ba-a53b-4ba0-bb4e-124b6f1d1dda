import tkinter as tk

class MyApp:
    def __init__(self):
        self.root = None  # 当前主窗口引用

    def start_first_window(self):
        # 创建第一个主窗口
        self.root = tk.Tk()
        self.root.title("First Window")

        btn = tk.Button(self.root, text="Open Second Window", command=self._open_second_window)
        btn.pack(padx=20, pady=20)

        # 关闭第一个窗口时退出程序
        self.root.protocol("WM_DELETE_WINDOW", self._on_first_window_close)

        self.root.mainloop()

    def _on_first_window_close(self):
        # 确保第一个窗口关闭时销毁当前窗口和退出主循环
        if self.root:
            self.root.destroy()
            self.root = None

    def _open_second_window(self):
        # 关闭第一个窗口后打开第二个主窗口
        if self.root:
            self.root.destroy()
            self.root = None

        # 创建新的主窗口实例
        self.root = tk.Tk()
        self.root.title("Second Window")

        btn_quit = tk.Button(self.root, text="Quit", command=self._quit_app)
        btn_quit.pack(padx=20, pady=20)

        # 关闭第二个窗口时退出程序
        self.root.protocol("WM_DELETE_WINDOW", self._quit_app)

        self.root.mainloop()

    def _quit_app(self):
        # 退出应用
        if self.root:
            self.root.destroy()
            self.root = None

if __name__ == "__main__":
    app = MyApp()
    app.start_first_window()
