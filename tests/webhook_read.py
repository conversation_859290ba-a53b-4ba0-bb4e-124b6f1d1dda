import time
from typing import List

import requests
from apscheduler.schedulers.background import BackgroundScheduler

import models.server_properties
from config import constants
from models.server_data import TokenExpiredError

token_url = 'http://localhost:8000/webhook/token'
get_unread_api_url = 'http://localhost:8000/webhook/unread'


def read_by_client_id_request(token: str, client_id: str, size: int) -> List[str]:
    print(f"正在获取{client_id}的未读消息...")
    headers = {'Authorization': f'Bearer {token}'}
    params = {'client_key': client_id, 'size': size}
    response = requests.get(get_unread_api_url, headers=headers, params=params)
    if response.status_code != 200:
        raise TokenExpiredError()
    data = response.json()
    return [msg['message'] for msg in data.get('messages', [])]


def get_token(api_key_param: str) -> str:
    headers = {'Authorization': f'Bearer {api_key_param}'}
    response = requests.get(token_url, headers=headers)
    if response.status_code != 200:
        raise Exception('获取token失败')
    token = response.json()['token']
    if not token:
        raise Exception('获取token失败')
    return token


# 从api_url获取所属client_id[允许为空]未读1条消息,如果一直没有消息,则每隔3秒请求一次直到有消息为止,然后返回
# api_url是get请求,其中header传递Authorization: Bearer token,如果返回不是200,则使用token_url获取token,再次请求api_url
# token_url是get请求,其中只需要传递参数api_key,返回token
def read_by_client_id(api_key_param: str, client_id: str = None) -> str:
    # 获取token
    token = get_token(api_key_param)
    size = 100
    while True:
        try:
            unread_msgs = read_by_client_id_request(token, client_id, size)
        except TokenExpiredError:
            print("token过期,重新获取token...")
            # 重新获取token
            token = get_token(api_key_param)
            unread_msgs = read_by_client_id_request(token, client_id, size)
        if unread_msgs:
            print(f"获取到{client_id}的未读消息: {unread_msgs}")
            return unread_msgs[0]

        print("当前没有未读消息,等待3秒后再次请求...")
        time.sleep(3)


def read_one_msg(api_key_param: str, client_keys_param: set[str]):
    print(f"client_key_set: {client_keys_param}")
    for client_key in client_keys_param:
        unread_msg = read_by_client_id(api_key_param, client_key)
        print(f"client_key: {client_key}, unread_msg: {unread_msg}")


def read_all_msg(api_key_param: str):
    while True:
        print(f"messages:{read_by_client_id(api_key_param, None)}")


if __name__ == '__main__':
    server_properties = models.server_properties.ServerProperties("../resources/server_config.ini", BackgroundScheduler(
        timezone=constants.DEFAULT_TIMEZONE))
    api_key = server_properties.server_config['api_key']
    # api_key = 'x'
    client_keys = server_properties.client_info_properties.keys()
    read_one_msg(api_key, client_keys)
    # read_all_msg(api_key)
