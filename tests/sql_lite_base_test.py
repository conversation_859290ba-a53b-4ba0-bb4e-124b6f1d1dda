# sql基础操作类的测试
import logging
import os
import sqlite3
import time

from models.sqlite_base_manager import SQLiteBaseManager
from utils import server_utils

# 配置 logger
logging.basicConfig(
    level=logging.DEBUG,  # 设置日志级别（DEBUG 及以上级别的日志都会被记录）
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 日志格式
    datefmt='%Y-%m-%d %H:%M:%S'  # 时间格式
)

logger = logging.getLogger(__name__)

class SQLiteBaseManager1(SQLiteBaseManager):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        server_utils.logger_print(msg="calling database initialization",custom_logger=logger)

    def _init_db(self):
        # 初始化数据库:[新建表,创建索引,设置WAL/NORMAL模式]
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")
            init_sql = """
                CREATE TABLE IF NOT EXISTS db1 (
                    id TEXT NOT NULL PRIMARY KEY,
                    message TEXT NOT NULL ,
                    client_key TEXT NOT NULL
                );
                """
            conn.executescript(init_sql)
            conn.commit()
class SQLiteBaseManager2(SQLiteBaseManager):
    def __init__(self, db_path: str):
        super().__init__(db_path)

    def _init_db(self):
        # 初始化数据库:[新建表,创建索引,设置WAL/NORMAL模式]
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")
            init_sql = """
                CREATE TABLE IF NOT EXISTS db2 (
                    id TEXT NOT NULL PRIMARY KEY,
                    message TEXT NOT NULL ,
                    client_key TEXT NOT NULL
                );
                """
            conn.executescript(init_sql)
            conn.commit()

if __name__ == '__main__':
    # 多用例定时任务测试
    db_path = os.path.join(os.path.expanduser("~"), ".webhook_server", "test.db")
    sql_lite_base_manager1_1 = SQLiteBaseManager1(db_path)
    sql_lite_base_manager1_2 = SQLiteBaseManager1(db_path)
    sql_lite_base_manager2_1 = SQLiteBaseManager2(db_path)
    while True:
        time.sleep(1)

