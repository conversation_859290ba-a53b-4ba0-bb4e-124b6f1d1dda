铁路沿线  https://www.aliyundrive.com/s/riWx5h9X3vJ
激流中国 

将所有需要的学习资料复制到硬盘柜中 --- 本地的，U盘内的，云盘内的
所有待做事项迁移到本地
-------------------------------
https://ttkbootstrap.readthedocs.io/en/latest/styleguide/spinbox/

ttkbootstrap：
弹窗  Messagebox
Frame 是固定的，其中数据是在表格中，使用Tableview
配置加载界面中表格不新增功能
设备数据显示列表中不需要分页,其数据实时刷新;不需要额外的功能

使用ttkbootstrap.tooltip替代自定义的提示信息 --- 使用原生的提示工具


参考其中的关于计时显示的逻辑，优化当前的服务器运行时间相关代码 https://ttkbootstrap.readthedocs.io/en/latest/gallery/stopwatch/#example-code
cpu使用率使用meter
主界面中的注意事项可折叠
自定义配置项需要添加验证逻辑，错误显示红边框
-------------------------------

刷新之后下了应该回到顶部
按钮在点击之后不应该有按下的样式
统一所有控件字体
配置加载界面中：
可用和占用的圆点是有颜色区分的
四个按钮居中排布

PyInstaller打包带有gui界面的项目
要求：
1. 一整个项目文件打包，其中包含资源文件
2. Linux deb和Windows exe
3. 使用spec自定义打包行为


该代码是在主界面上顶部显示滚动的通知栏
使用tkbootstrap 改造该代码文件

悬停行显示的路径的"径"字体很怪

全流程测试，混合命令行gui+命令行server+idea运行
在测试过程中，检测有没有慢的响应


_wait_for_shutdown中为什么需要设置0.4才不报错，实际测试 cleanup_on_shutdown 执行不需要0.02 s


参考 https://github.com/nmhjklnm/sms_server 项目进行功能补充和优化

所以的import都需要加上代码文件开头,避免引入时出现循环导入的情况  本项目的import后只能是文件名不能是类名称
删除完全没有使用的函数方法和注释掉的代码
使用ai，给代码文件重命名,调整代码函数在文件内的顺序:__init__函数在最上面,只在文件内使用的函数第二,静态函数中间,可以给外部使用的函数在最后
将代码中注释的注意事项和根据代码分析得到的注意事项总结成说明文档

python D:\Git\python-samples-hub\src\config_selection_gui.py
python D:\Git\python-samples-hub\src\webhook_server_gui.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini
python D:\Git\python-samples-hub\src\webhook_server_command.py --config C:\Users\<USER>\.webhook_server\server_config_4.ini

