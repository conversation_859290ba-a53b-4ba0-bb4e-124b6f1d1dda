# 说明

> 本项目是保存自己有用的python代码。

## requirements文件依赖精简

> 由于本项目不大且防止出现父依赖和子依赖都出现的情况,这里就使用对应的方法步骤生成精简版本的requirements文件。
> 1. 安装`pipreqs`包,命令`pip install pipreqs`。
> 2. 进入项目目录,执行命令`pipreqs . --force` , 生成requirements.txt文件。
> 3. 执行命令`pip install -r requirements.txt`。

## 各文件作用

> `webhook_server.py`
> 文件是局域网中短信转发的接收端，用于接收短信并让在该局域网中指定设备接收,当然也可以接收局域网其他信息内容,就一个临时的局域网数据暂存器。  
> `client_identity_data.conf`文件是`webhook_server.py`
> 代码文件发送方的标识数据的配置文件,里面存储了发信方的标识和描述信息,用于限制范围内的发信方才能发信到该服务器和区分不同的发信方。  
> `server_config.json`文件是`webhook_server.py`
>
代码文件服务器的配置文件,里面存储了各种服务器参数,包括接收的第三方获取token的api_key、接收方白名单IP、发信方标识配置文件路径、日志文件目录路径、日志文件过期天数、服务器关闭时未读信息暂存文件路径、监听地址、端口和服务器运行时间段。

### server_config.json文件配置项说明

```
{
  "api_key": "your_api_key",
  "whitelist": [
    "127.0.0.1"
  ],
  "client_config_path": "../config/client_identity_data.conf",
  "persistent_file_path": "../data/messages.json",
  "log_dir_path": "../logs",
  "expire_logs_days": 5,
  "host": "0.0.0.0",
  "port": 8000,
  "log_color": false,
  "run_time": "07:00-23:50"
}
```

- `api_key`：第三方获取token的api_key,用于验证接收未读信息的请求。
- `ip_whitelist`：接收方白名单IP,只有在白名单内的IP才能接收未读信息。
- `client_identity_data_path`：发信方标识配置文件路径,不能为空且文件必须存在且存在内容。
- `persistent_file_path`：服务器关闭时未读信息暂存文件路径。
- `log_path`：日志文件目录路径。
- `expire_logs_days`：日志文件过期天数,小于等于0时表示永久保存。
- `host`：运行地址。
- `port`：端口。
- `log_color`：控制台是否输出彩色日志。
- `run_time`：服务器运行时间段,在这个时间段之外服务器启动会失败。

### 新增测试用例文件预新增内容

```pycon
import logging
import os
import sys

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

logger = logging.getLogger(__name__)

```

> 测试用例目前使用 `pytest`库,不使用`unittest`库。 
